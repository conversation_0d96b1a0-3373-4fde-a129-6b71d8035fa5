# @babel/plugin-proposal-private-property-in-object

> ⚠️ This version of the package (`v7.21.0-placeholder-for-preset-env.1`) is not meant to
> be imported. Use any other version of this plugin or, even better, the
> [@babel/plugin-transform-private-property-in-object](https://babeljs.io/docs/en/babel-plugin-transform-private-property-in-object) package.

> This plugin transforms checks for a private property in an object

See our website [@babel/plugin-proposal-private-property-in-object](https://babeljs.io/docs/en/babel-plugin-proposal-private-property-in-object) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-proposal-private-property-in-object
```

or using yarn:

```sh
yarn add @babel/plugin-proposal-private-property-in-object --dev
```
